<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DashboardService
{
    public function getSystemMetrics()
    {
        return Cache::remember('dashboard.system_metrics', 300, function () {
            $metrics = [
                'environment' => app()->environment(),
                'laravel_version' => app()->version(),
                'php_version' => PHP_VERSION,
                'timezone' => config('app.timezone'),
                'timestamp' => now()->format('Y-m-d H:i:s T'),
                'memory_usage' => $this->getMemoryUsage(),
                'disk_space' => $this->getDiskSpace(),
                'uptime' => $this->getSystemUptime(),
                'cache_driver' => config('cache.default'),
                'session_driver' => config('session.driver'),
                'primary_db_status' => $this->testDatabaseConnection()
            ];

            return $metrics;
        });
    }

    public function testAllDatabaseConnections()
    {
        return Cache::remember('dashboard.db_connections', 600, function () {
            $connections = config('database.connections');
            $results = [];

            foreach ($connections as $name => $config) {
                $connectionInfo = [
                    'name' => $name,
                    'driver' => $config['driver'],
                    'host' => $config['host'] ?? 'N/A',
                    'database' => $config['database'] ?? 'N/A',
                    'status' => 'untested',
                    'table_count' => 0,
                    'error' => null,
                    'response_time' => 0
                ];

                try {
                    $start = microtime(true);
                    $pdo = DB::connection($name)->getPdo();
                    $connectionInfo['response_time'] = round((microtime(true) - $start) * 1000, 2);
                    $connectionInfo['status'] = 'connected';
                    
                    // Get table count
                    $connectionInfo['table_count'] = $this->getTableCount($name, $config['driver']);
                    
                } catch (\Exception $e) {
                    $connectionInfo['status'] = 'failed';
                    $connectionInfo['error'] = $e->getMessage();
                }

                $results[] = $connectionInfo;
            }

            return $results;
        });
    }

    public function getScheduledTasksInfo()
    {
        return Cache::remember('dashboard.scheduled_tasks', 3600, function () {
            try {
                // Get the kernel instance and create a fresh schedule
                $kernel = app(\App\Console\Kernel::class);
                $schedule = app(Schedule::class);

                // Call the schedule method to populate the schedule with tasks
                $reflection = new \ReflectionClass($kernel);
                $scheduleMethod = $reflection->getMethod('schedule');
                $scheduleMethod->setAccessible(true);
                $scheduleMethod->invoke($kernel, $schedule);

                $tasks = [];

                // Get scheduled tasks using reflection
                $reflection = new \ReflectionClass($schedule);
                $eventsProperty = $reflection->getProperty('events');
                $eventsProperty->setAccessible(true);
                $events = $eventsProperty->getValue($schedule);

                foreach ($events as $event) {
                    $command = $this->getEventCommand($event);
                    $tasks[] = [
                        'command' => $command,
                        'expression' => $event->expression,
                        'human_readable' => $this->cronToHuman($event->expression),
                        'description' => $event->description ?? 'No description',
                        'timezone' => $event->timezone ?? config('app.timezone'),
                        'next_run' => $this->getNextRunTime($event->expression),
                        'last_run' => 'N/A' // This would require implementing a log system
                    ];
                }

                return $tasks;
            } catch (\Exception $e) {
                Log::error('Failed to get scheduled tasks: ' . $e->getMessage());
                return [];
            }
        });
    }

    public function getArtisanCommands()
    {
        return Cache::remember('dashboard.artisan_commands', 3600, function () {
            try {
                $commands = Artisan::all();
                $result = [];

                foreach ($commands as $name => $command) {
                    $result[] = [
                        'signature' => $name,
                        'description' => $command->getDescription() ?: 'No description available',
                        'category' => $this->getCommandCategory($name),
                        'arguments' => $this->getCommandArguments($command),
                        'options' => $this->getCommandOptions($command),
                        'usage_example' => "php artisan {$name}"
                    ];
                }

                return collect($result)->sortBy('category')->values()->toArray();
            } catch (\Exception $e) {
                Log::error('Failed to get Artisan commands: ' . $e->getMessage());
                return [];
            }
        });
    }

    public function getRecentLogs()
    {
        try {
            $logPath = storage_path('logs/laravel.log');
            $logs = [];

            if (file_exists($logPath)) {
                $lines = array_slice(file($logPath), -100); // Get last 100 lines
                
                foreach (array_reverse($lines) as $line) {
                    if (preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \w+\.(\w+): (.+)/', $line, $matches)) {
                        $logs[] = [
                            'timestamp' => $matches[1],
                            'level' => strtoupper($matches[2]),
                            'message' => substr($matches[3], 0, 200) . (strlen($matches[3]) > 200 ? '...' : ''),
                            'full_message' => $matches[3]
                        ];
                    }
                }
            }

            return array_slice($logs, 0, 50); // Return last 50 log entries
        } catch (\Exception $e) {
            Log::error('Failed to get recent logs: ' . $e->getMessage());
            return [];
        }
    }

    private function testDatabaseConnection($connection = null)
    {
        try {
            DB::connection($connection)->getPdo();
            return 'connected';
        } catch (\Exception $e) {
            return 'failed';
        }
    }

    private function getTableCount($connectionName, $driver)
    {
        try {
            switch ($driver) {
                case 'mysql':
                    return DB::connection($connectionName)
                        ->select("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?", [
                            DB::connection($connectionName)->getDatabaseName()
                        ])[0]->count;
                
                case 'sqlite':
                    return DB::connection($connectionName)
                        ->select("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")[0]->count;
                
                case 'pgsql':
                    return DB::connection($connectionName)
                        ->select("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'public'")[0]->count;
                
                case 'sqlsrv':
                    return DB::connection($connectionName)
                        ->select("SELECT COUNT(*) as count FROM information_schema.tables")[0]->count;
                
                case 'oracle':
                    return DB::connection($connectionName)
                        ->select("SELECT COUNT(*) as count FROM user_tables")[0]->count;
                
                default:
                    return 0;
            }
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getMemoryUsage()
    {
        $bytes = memory_get_usage(true);
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        
        while ($bytes >= 1024 && $i < 3) {
            $bytes /= 1024;
            $i++;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    private function getDiskSpace()
    {
        try {
            $bytes = disk_free_space(storage_path());
            $units = ['B', 'KB', 'MB', 'GB', 'TB'];
            $i = 0;
            
            while ($bytes >= 1024 && $i < 4) {
                $bytes /= 1024;
                $i++;
            }
            
            return round($bytes, 2) . ' ' . $units[$i];
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    private function getSystemUptime()
    {
        try {
            if (function_exists('sys_getloadavg')) {
                $uptime = shell_exec('uptime');
                return trim($uptime) ?: 'Unknown';
            }
            return 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    private function getEventCommand($event)
    {
        try {
            $command = '';

            // Try to get the command from different possible properties
            if (isset($event->command)) {
                $command = $event->command;
            } elseif (method_exists($event, 'getSummaryForDisplay')) {
                $command = $event->getSummaryForDisplay();
            } elseif (method_exists($event, 'buildCommand')) {
                $command = $event->buildCommand();
            } else {
                // Fallback to reflection to get command details
                $reflection = new \ReflectionClass($event);

                if ($reflection->hasProperty('command')) {
                    $commandProperty = $reflection->getProperty('command');
                    $commandProperty->setAccessible(true);
                    $command = $commandProperty->getValue($event);
                }
            }

            if (!$command) {
                return 'Unknown command';
            }

            // Clean up the command display - extract just the artisan command part
            if (preg_match('/artisan["\s]+([^"]+)/', $command, $matches)) {
                return 'php artisan ' . trim($matches[1]);
            } elseif (preg_match('/artisan\s+([^\s]+(?:\s+[^\s]+)*)/', $command, $matches)) {
                return 'php artisan ' . $matches[1];
            }

            return $command;
        } catch (\Exception $e) {
            return 'Unknown command';
        }
    }

    private function cronToHuman($expression)
    {
        $parts = explode(' ', $expression);
        if (count($parts) !== 5) {
            return $expression;
        }

        [$minute, $hour, $day, $month, $dayOfWeek] = $parts;

        // Handle common patterns
        if ($expression === '* * * * *') {
            return 'Every minute';
        } elseif ($expression === '0 * * * *') {
            return 'Hourly';
        } elseif ($expression === '0 0 * * *') {
            return 'Daily at midnight';
        } elseif ($expression === '0 0 * * 0') {
            return 'Weekly on Sunday at midnight';
        } elseif ($expression === '0 0 1 * *') {
            return 'Monthly on the 1st at midnight';
        }

        // Handle every X minutes patterns
        if (preg_match('/^\*\/(\d+) \* \* \* \*$/', $expression, $matches)) {
            return "Every {$matches[1]} minutes";
        }

        // Handle every X minutes with time restrictions
        if (preg_match('/^\*\/(\d+) \* \* \* \*$/', $expression, $matches)) {
            return "Every {$matches[1]} minutes";
        }

        // Handle specific times
        if (preg_match('/^(\d+) (\d+) \* \* \*$/', $expression, $matches)) {
            return "Daily at {$matches[1]}:{$matches[0]} (24h format)";
        }

        // Handle monthly patterns
        if (preg_match('/^(\d+) (\d+) (\d+) \* \*$/', $expression, $matches)) {
            return "Monthly on day {$matches[2]} at {$matches[1]}:{$matches[0]}";
        }

        // Handle twice daily
        if (preg_match('/^0 (\d+),(\d+) \* \* \*$/', $expression, $matches)) {
            return "Twice daily at {$matches[0]}:00 and {$matches[1]}:00";
        }

        // Handle weekly patterns
        if (preg_match('/^(\d+) (\d+) \* \* (\d+)$/', $expression, $matches)) {
            $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            $dayName = $days[$matches[2]] ?? "day {$matches[2]}";
            return "Weekly on {$dayName} at {$matches[1]}:{$matches[0]}";
        }

        // Fallback to a more readable format
        $readable = [];

        if ($minute === '*') {
            $readable[] = 'every minute';
        } elseif (strpos($minute, '*/') === 0) {
            $readable[] = 'every ' . substr($minute, 2) . ' minutes';
        } else {
            $readable[] = "at minute {$minute}";
        }

        if ($hour !== '*') {
            if (strpos($hour, ',') !== false) {
                $readable[] = "at hours {$hour}";
            } else {
                $readable[] = "at hour {$hour}";
            }
        }

        if ($day !== '*') {
            $readable[] = "on day {$day} of month";
        }

        if ($month !== '*') {
            $readable[] = "in month {$month}";
        }

        if ($dayOfWeek !== '*') {
            $days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            $dayNames = array_map(function($d) use ($days) {
                return $days[$d] ?? $d;
            }, explode(',', $dayOfWeek));
            $readable[] = 'on ' . implode(', ', $dayNames);
        }

        return ucfirst(implode(' ', $readable));
    }

    private function getNextRunTime($expression)
    {
        try {
            $now = Carbon::now();
            $parts = explode(' ', $expression);

            if (count($parts) !== 5) {
                return 'Invalid expression';
            }

            [$minute, $hour, $day, $month, $dayOfWeek] = $parts;

            // Handle common patterns with better calculations
            if ($expression === '* * * * *') {
                return $now->copy()->addMinute()->format('Y-m-d H:i:s');
            } elseif ($expression === '0 * * * *') {
                return $now->copy()->startOfHour()->addHour()->format('Y-m-d H:i:s');
            } elseif ($expression === '0 0 * * *') {
                return $now->copy()->startOfDay()->addDay()->format('Y-m-d H:i:s');
            } elseif (preg_match('/^\*\/(\d+) \* \* \* \*$/', $expression, $matches)) {
                // Handle every X minutes
                $interval = (int)$matches[1];
                $nextRun = $now->copy();
                $currentMinute = $nextRun->minute;
                $nextMinute = ceil(($currentMinute + 1) / $interval) * $interval;

                if ($nextMinute >= 60) {
                    $nextRun->addHour()->minute(0);
                } else {
                    $nextRun->minute($nextMinute);
                }

                return $nextRun->format('Y-m-d H:i:s');
            } elseif (preg_match('/^(\d+) (\d+) \* \* \*$/', $expression, $matches)) {
                // Handle daily at specific time
                $targetMinute = (int)$matches[1];
                $targetHour = (int)$matches[2];

                $nextRun = $now->copy()->hour($targetHour)->minute($targetMinute)->second(0);

                if ($nextRun <= $now) {
                    $nextRun->addDay();
                }

                return $nextRun->format('Y-m-d H:i:s');
            } else {
                // For complex expressions, provide a relative time description
                return 'Calculated by Laravel scheduler';
            }
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    private function getCommandCategory($name)
    {
        $parts = explode(':', $name);
        return $parts[0] ?? 'general';
    }

    private function getCommandArguments($command)
    {
        try {
            $definition = $command->getDefinition();
            $arguments = [];
            
            foreach ($definition->getArguments() as $argument) {
                $arguments[] = [
                    'name' => $argument->getName(),
                    'required' => $argument->isRequired(),
                    'description' => $argument->getDescription()
                ];
            }
            
            return $arguments;
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getCommandOptions($command)
    {
        try {
            $definition = $command->getDefinition();
            $options = [];
            
            foreach ($definition->getOptions() as $option) {
                $options[] = [
                    'name' => $option->getName(),
                    'shortcut' => $option->getShortcut(),
                    'description' => $option->getDescription()
                ];
            }
            
            return $options;
        } catch (\Exception $e) {
            return [];
        }
    }
}